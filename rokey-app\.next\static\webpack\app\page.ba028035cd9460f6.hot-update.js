"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/landing/AIIntegrationsSection.tsx":
/*!**********************************************************!*\
  !*** ./src/components/landing/AIIntegrationsSection.tsx ***!
  \**********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AIIntegrationsSection)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nconst aiProviders = [\n    // Row 1 - Only the most essential providers with guaranteed working logos\n    {\n        name: 'OpenAI',\n        logo: 'https://raw.githubusercontent.com/lobehub/lobe-icons/master/packages/static-png/dark/openai.png',\n        alt: 'OpenAI GPT Models'\n    },\n    {\n        name: 'Anthropic',\n        logo: 'https://raw.githubusercontent.com/lobehub/lobe-icons/master/packages/static-png/dark/claude.png',\n        alt: 'Anthropic Claude'\n    },\n    {\n        name: 'Google',\n        logo: 'https://raw.githubusercontent.com/lobehub/lobe-icons/master/packages/static-png/dark/google-color.png',\n        alt: 'Google Gemini'\n    },\n    {\n        name: 'Meta',\n        logo: 'https://raw.githubusercontent.com/lobehub/lobe-icons/master/packages/static-png/dark/meta-color.png',\n        alt: 'Meta Llama Models'\n    },\n    {\n        name: 'Mistral AI',\n        logo: 'https://raw.githubusercontent.com/lobehub/lobe-icons/master/packages/static-png/dark/mistral-color.png',\n        alt: 'Mistral AI'\n    },\n    {\n        name: 'DeepSeek',\n        logo: 'https://raw.githubusercontent.com/lobehub/lobe-icons/master/packages/static-png/dark/deepseek.png',\n        alt: 'DeepSeek'\n    },\n    {\n        name: 'Microsoft',\n        logo: 'https://raw.githubusercontent.com/lobehub/lobe-icons/master/packages/static-png/dark/azure-color.png',\n        alt: 'Microsoft Azure'\n    },\n    {\n        name: 'Cohere',\n        logo: 'https://raw.githubusercontent.com/lobehub/lobe-icons/master/packages/static-png/dark/cohere-color.png',\n        alt: 'Cohere'\n    },\n    {\n        name: 'Perplexity',\n        logo: 'https://raw.githubusercontent.com/lobehub/lobe-icons/master/packages/static-png/dark/perplexity-color.png',\n        alt: 'Perplexity'\n    },\n    {\n        name: 'xAI',\n        logo: 'https://raw.githubusercontent.com/lobehub/lobe-icons/master/packages/static-png/dark/xai.png',\n        alt: 'xAI Grok'\n    },\n    {\n        name: 'NVIDIA',\n        logo: 'https://raw.githubusercontent.com/lobehub/lobe-icons/master/packages/static-png/dark/nvidia-color.png',\n        alt: 'NVIDIA'\n    },\n    {\n        name: 'Groq',\n        logo: 'https://raw.githubusercontent.com/lobehub/lobe-icons/master/packages/static-png/dark/groq.png',\n        alt: 'Groq'\n    },\n    // Row 2 - Safe additional providers\n    {\n        name: 'AI21 Labs',\n        logo: 'https://raw.githubusercontent.com/lobehub/lobe-icons/master/packages/static-png/dark/ai21.png',\n        alt: 'AI21 Labs'\n    },\n    {\n        name: 'Amazon',\n        logo: 'https://raw.githubusercontent.com/lobehub/lobe-icons/master/packages/static-png/dark/aws-color.png',\n        alt: 'Amazon Bedrock'\n    },\n    {\n        name: 'Alibaba',\n        logo: 'https://raw.githubusercontent.com/lobehub/lobe-icons/master/packages/static-png/dark/alibaba-color.png',\n        alt: 'Alibaba Qwen Models'\n    },\n    {\n        name: 'Zhipu AI',\n        logo: 'https://raw.githubusercontent.com/lobehub/lobe-icons/master/packages/static-png/dark/zhipu-color.png',\n        alt: 'Zhipu AI GLM'\n    },\n    {\n        name: 'Yi AI',\n        logo: 'https://raw.githubusercontent.com/lobehub/lobe-icons/master/packages/static-png/dark/yi-color.png',\n        alt: 'Yi AI'\n    },\n    {\n        name: 'Hugging Face',\n        logo: 'https://raw.githubusercontent.com/lobehub/lobe-icons/master/packages/static-png/dark/huggingface-color.png',\n        alt: 'Hugging Face'\n    },\n    {\n        name: 'Wenxin',\n        logo: 'https://raw.githubusercontent.com/lobehub/lobe-icons/master/packages/static-png/dark/wenxin-color.png',\n        alt: 'Baidu Wenxin'\n    },\n    {\n        name: 'Spark',\n        logo: 'https://raw.githubusercontent.com/lobehub/lobe-icons/master/packages/static-png/dark/spark-color.png',\n        alt: 'iFlytek Spark'\n    },\n    {\n        name: 'Novita',\n        logo: 'https://raw.githubusercontent.com/lobehub/lobe-icons/master/packages/static-png/dark/novita-color.png',\n        alt: 'Novita AI'\n    },\n    {\n        name: 'Minimax',\n        logo: 'https://raw.githubusercontent.com/lobehub/lobe-icons/master/packages/static-png/dark/minimax-color.png',\n        alt: 'Minimax'\n    },\n    {\n        name: 'Baichuan',\n        logo: 'https://raw.githubusercontent.com/lobehub/lobe-icons/master/packages/static-png/dark/baichuan-color.png',\n        alt: 'Baichuan AI'\n    },\n    {\n        name: 'Stepfun',\n        logo: 'https://raw.githubusercontent.com/lobehub/lobe-icons/master/packages/static-png/dark/stepfun-color.png',\n        alt: 'Stepfun'\n    }\n];\nfunction AIIntegrationsSection() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"relative py-20 overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0\",\n                style: {\n                    background: \"radial-gradient(ellipse at center, #1C051C 0%, #040716 70%)\"\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\AIIntegrationsSection.tsx\",\n                lineNumber: 40,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 opacity-20\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        backgroundImage: \"\\n              linear-gradient(rgba(255, 255, 255, 0.05) 1px, transparent 1px),\\n              linear-gradient(90deg, rgba(255, 255, 255, 0.05) 1px, transparent 1px)\\n            \",\n                        backgroundSize: '50px 50px'\n                    }\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\AIIntegrationsSection.tsx\",\n                    lineNumber: 49,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\AIIntegrationsSection.tsx\",\n                lineNumber: 48,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 20\n                        },\n                        whileInView: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        viewport: {\n                            once: true\n                        },\n                        transition: {\n                            duration: 0.6\n                        },\n                        className: \"text-center mb-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-4xl md:text-5xl font-bold text-white mb-6\",\n                                children: [\n                                    \"Connect to any AI model with\",\n                                    ' ',\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-transparent bg-clip-text bg-gradient-to-r from-[#ff6b35] to-[#f7931e]\",\n                                        children: \"300+ integrations\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\AIIntegrationsSection.tsx\",\n                                        lineNumber: 71,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\AIIntegrationsSection.tsx\",\n                                lineNumber: 69,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xl text-gray-300 max-w-3xl mx-auto\",\n                                children: \"RouKey provides unified access to every major AI provider through a single API. No vendor lock-in, just seamless AI integration.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\AIIntegrationsSection.tsx\",\n                                lineNumber: 75,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\AIIntegrationsSection.tsx\",\n                        lineNumber: 62,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 30\n                        },\n                        whileInView: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        viewport: {\n                            once: true\n                        },\n                        transition: {\n                            duration: 0.8,\n                            delay: 0.2\n                        },\n                        className: \"grid grid-cols-6 md:grid-cols-12 gap-4 mb-8\",\n                        children: aiProviders.slice(0, 12).map((provider, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    scale: 0.8\n                                },\n                                whileInView: {\n                                    opacity: 1,\n                                    scale: 1\n                                },\n                                viewport: {\n                                    once: true\n                                },\n                                transition: {\n                                    duration: 0.4,\n                                    delay: index * 0.05\n                                },\n                                className: \"group relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-12 h-12 md:w-16 md:h-16 bg-white/10 backdrop-blur-sm rounded-xl border border-white/20 flex items-center justify-center hover:bg-white/20 transition-all duration-300 hover:scale-110\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-8 h-8 md:w-10 md:h-10 bg-white rounded-lg flex items-center justify-center p-1\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                src: provider.logo,\n                                                alt: provider.alt,\n                                                width: 32,\n                                                height: 32,\n                                                className: \"w-full h-full object-contain\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\AIIntegrationsSection.tsx\",\n                                                lineNumber: 100,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\AIIntegrationsSection.tsx\",\n                                            lineNumber: 99,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\AIIntegrationsSection.tsx\",\n                                        lineNumber: 98,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute -top-10 left-1/2 transform -translate-x-1/2 bg-black/80 text-white text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 whitespace-nowrap\",\n                                        children: provider.alt\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\AIIntegrationsSection.tsx\",\n                                        lineNumber: 111,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, provider.name, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\AIIntegrationsSection.tsx\",\n                                lineNumber: 90,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\AIIntegrationsSection.tsx\",\n                        lineNumber: 82,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 30\n                        },\n                        whileInView: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        viewport: {\n                            once: true\n                        },\n                        transition: {\n                            duration: 0.8,\n                            delay: 0.4\n                        },\n                        className: \"grid grid-cols-6 md:grid-cols-12 gap-4 mb-12\",\n                        children: aiProviders.slice(12, 24).map((provider, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    scale: 0.8\n                                },\n                                whileInView: {\n                                    opacity: 1,\n                                    scale: 1\n                                },\n                                viewport: {\n                                    once: true\n                                },\n                                transition: {\n                                    duration: 0.4,\n                                    delay: index * 0.05\n                                },\n                                className: \"group relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-12 h-12 md:w-16 md:h-16 bg-white/10 backdrop-blur-sm rounded-xl border border-white/20 flex items-center justify-center hover:bg-white/20 transition-all duration-300 hover:scale-110\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-8 h-8 md:w-10 md:h-10 bg-white rounded-lg flex items-center justify-center p-1\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                src: provider.logo,\n                                                alt: provider.alt,\n                                                width: 32,\n                                                height: 32,\n                                                className: \"w-full h-full object-contain\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\AIIntegrationsSection.tsx\",\n                                                lineNumber: 137,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\AIIntegrationsSection.tsx\",\n                                            lineNumber: 136,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\AIIntegrationsSection.tsx\",\n                                        lineNumber: 135,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute -top-10 left-1/2 transform -translate-x-1/2 bg-black/80 text-white text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 whitespace-nowrap\",\n                                        children: provider.alt\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\AIIntegrationsSection.tsx\",\n                                        lineNumber: 148,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, provider.name, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\AIIntegrationsSection.tsx\",\n                                lineNumber: 127,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\AIIntegrationsSection.tsx\",\n                        lineNumber: 119,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 20\n                        },\n                        whileInView: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        viewport: {\n                            once: true\n                        },\n                        transition: {\n                            duration: 0.6,\n                            delay: 0.6\n                        },\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-400 mb-6\",\n                                children: \"And 270+ more providers available through RouKey's unified API\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\AIIntegrationsSection.tsx\",\n                                lineNumber: 163,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col sm:flex-row gap-4 justify-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"px-8 py-3 bg-gradient-to-r from-[#ff6b35] to-[#f7931e] text-white font-semibold rounded-xl hover:shadow-lg hover:shadow-[#ff6b35]/25 transition-all duration-300 hover:scale-105\",\n                                        children: \"View All Integrations\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\AIIntegrationsSection.tsx\",\n                                        lineNumber: 167,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"px-8 py-3 border border-white/20 text-white font-semibold rounded-xl hover:bg-white/10 transition-all duration-300\",\n                                        children: \"Start Free Trial\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\AIIntegrationsSection.tsx\",\n                                        lineNumber: 170,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\AIIntegrationsSection.tsx\",\n                                lineNumber: 166,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\AIIntegrationsSection.tsx\",\n                        lineNumber: 156,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\AIIntegrationsSection.tsx\",\n                lineNumber: 60,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\AIIntegrationsSection.tsx\",\n        lineNumber: 38,\n        columnNumber: 5\n    }, this);\n}\n_c = AIIntegrationsSection;\nvar _c;\n$RefreshReg$(_c, \"AIIntegrationsSection\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/landing/AIIntegrationsSection.tsx\n"));

/***/ })

});