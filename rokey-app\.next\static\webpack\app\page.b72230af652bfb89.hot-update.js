"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/landing/AIIntegrationsSection.tsx":
/*!**********************************************************!*\
  !*** ./src/components/landing/AIIntegrationsSection.tsx ***!
  \**********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AIIntegrationsSection)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nconst aiProviders = [\n    // Row 1 - Top providers by model count\n    {\n        name: 'OpenAI',\n        logo: 'https://raw.githubusercontent.com/lobehub/lobe-icons/master/packages/static-png/dark/openai.png',\n        alt: 'OpenAI GPT Models'\n    },\n    {\n        name: 'Alibaba',\n        logo: 'https://raw.githubusercontent.com/lobehub/lobe-icons/master/packages/static-png/dark/alibaba-color.png',\n        alt: 'Alibaba Qwen Models'\n    },\n    {\n        name: 'Mistral AI',\n        logo: 'https://raw.githubusercontent.com/lobehub/lobe-icons/master/packages/static-png/dark/mistral-color.png',\n        alt: 'Mistral AI'\n    },\n    {\n        name: 'Meta',\n        logo: 'https://raw.githubusercontent.com/lobehub/lobe-icons/master/packages/static-png/dark/meta-color.png',\n        alt: 'Meta Llama Models'\n    },\n    {\n        name: 'Anthropic',\n        logo: 'https://raw.githubusercontent.com/lobehub/lobe-icons/master/packages/static-png/dark/claude.png',\n        alt: 'Anthropic Claude'\n    },\n    {\n        name: 'DeepSeek',\n        logo: 'https://raw.githubusercontent.com/lobehub/lobe-icons/master/packages/static-png/dark/deepseek.png',\n        alt: 'DeepSeek'\n    },\n    {\n        name: 'Google',\n        logo: 'https://raw.githubusercontent.com/lobehub/lobe-icons/master/packages/static-png/dark/google-color.png',\n        alt: 'Google Gemini'\n    },\n    {\n        name: 'Microsoft',\n        logo: 'https://raw.githubusercontent.com/lobehub/lobe-icons/master/packages/static-png/dark/azure-color.png',\n        alt: 'Microsoft'\n    },\n    {\n        name: 'Cohere',\n        logo: 'https://raw.githubusercontent.com/lobehub/lobe-icons/master/packages/static-png/dark/cohere-color.png',\n        alt: 'Cohere'\n    },\n    {\n        name: 'Perplexity',\n        logo: 'https://raw.githubusercontent.com/lobehub/lobe-icons/master/packages/static-png/dark/perplexity-color.png',\n        alt: 'Perplexity'\n    },\n    {\n        name: 'xAI',\n        logo: 'https://raw.githubusercontent.com/lobehub/lobe-icons/master/packages/static-png/dark/xai.png',\n        alt: 'xAI Grok'\n    },\n    {\n        name: 'NVIDIA',\n        logo: 'https://raw.githubusercontent.com/lobehub/lobe-icons/master/packages/static-png/dark/nvidia-color.png',\n        alt: 'NVIDIA'\n    },\n    // Row 2 - Additional providers\n    {\n        name: 'AI21 Labs',\n        logo: 'https://raw.githubusercontent.com/lobehub/lobe-icons/master/packages/static-png/dark/ai21.png',\n        alt: 'AI21 Labs'\n    },\n    {\n        name: 'Liquid AI',\n        logo: 'https://raw.githubusercontent.com/lobehub/lobe-icons/master/packages/static-png/dark/liquid-color.png',\n        alt: 'Liquid AI'\n    },\n    {\n        name: 'Amazon',\n        logo: 'https://raw.githubusercontent.com/lobehub/lobe-icons/master/packages/static-png/dark/aws-color.png',\n        alt: 'Amazon Bedrock'\n    },\n    {\n        name: 'Zhipu AI',\n        logo: 'https://raw.githubusercontent.com/lobehub/lobe-icons/master/packages/static-png/dark/zhipu-color.png',\n        alt: 'Zhipu AI GLM'\n    },\n    {\n        name: 'Yi AI',\n        logo: 'https://raw.githubusercontent.com/lobehub/lobe-icons/master/packages/static-png/dark/yi-color.png',\n        alt: 'Yi AI'\n    },\n    {\n        name: 'Hugging Face',\n        logo: 'https://raw.githubusercontent.com/lobehub/lobe-icons/master/packages/static-png/dark/huggingface-color.png',\n        alt: 'Hugging Face'\n    },\n    {\n        name: 'Groq',\n        logo: 'https://raw.githubusercontent.com/lobehub/lobe-icons/master/packages/static-png/dark/groq.png',\n        alt: 'Groq'\n    },\n    {\n        name: 'Together AI',\n        logo: 'https://raw.githubusercontent.com/lobehub/lobe-icons/master/packages/static-png/dark/togetherai-color.png',\n        alt: 'Together AI'\n    },\n    {\n        name: 'Fireworks',\n        logo: 'https://raw.githubusercontent.com/lobehub/lobe-icons/master/packages/static-png/dark/fireworksai-color.png',\n        alt: 'Fireworks AI'\n    },\n    {\n        name: 'Replicate',\n        logo: 'https://raw.githubusercontent.com/lobehub/lobe-icons/master/packages/static-png/dark/replicate-color.png',\n        alt: 'Replicate'\n    },\n    {\n        name: 'Cerebras',\n        logo: 'https://raw.githubusercontent.com/lobehub/lobe-icons/master/packages/static-png/dark/cerebras-color.png',\n        alt: 'Cerebras'\n    },\n    {\n        name: 'SambaNova',\n        logo: 'https://raw.githubusercontent.com/lobehub/lobe-icons/master/packages/static-png/dark/sambanova-color.png',\n        alt: 'SambaNova'\n    }\n];\nfunction AIIntegrationsSection() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"relative py-20 overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0\",\n                style: {\n                    background: \"radial-gradient(ellipse at center, #1C051C 0%, #040716 70%)\"\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\AIIntegrationsSection.tsx\",\n                lineNumber: 40,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 opacity-20\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        backgroundImage: \"\\n              linear-gradient(rgba(255, 255, 255, 0.05) 1px, transparent 1px),\\n              linear-gradient(90deg, rgba(255, 255, 255, 0.05) 1px, transparent 1px)\\n            \",\n                        backgroundSize: '50px 50px'\n                    }\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\AIIntegrationsSection.tsx\",\n                    lineNumber: 49,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\AIIntegrationsSection.tsx\",\n                lineNumber: 48,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 20\n                        },\n                        whileInView: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        viewport: {\n                            once: true\n                        },\n                        transition: {\n                            duration: 0.6\n                        },\n                        className: \"text-center mb-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-4xl md:text-5xl font-bold text-white mb-6\",\n                                children: [\n                                    \"Connect to any AI model with\",\n                                    ' ',\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-transparent bg-clip-text bg-gradient-to-r from-[#ff6b35] to-[#f7931e]\",\n                                        children: \"300+ integrations\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\AIIntegrationsSection.tsx\",\n                                        lineNumber: 71,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\AIIntegrationsSection.tsx\",\n                                lineNumber: 69,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xl text-gray-300 max-w-3xl mx-auto\",\n                                children: \"RouKey provides unified access to every major AI provider through a single API. No vendor lock-in, just seamless AI integration.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\AIIntegrationsSection.tsx\",\n                                lineNumber: 75,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\AIIntegrationsSection.tsx\",\n                        lineNumber: 62,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 30\n                        },\n                        whileInView: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        viewport: {\n                            once: true\n                        },\n                        transition: {\n                            duration: 0.8,\n                            delay: 0.2\n                        },\n                        className: \"grid grid-cols-6 md:grid-cols-12 gap-4 mb-8\",\n                        children: aiProviders.slice(0, 12).map((provider, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    scale: 0.8\n                                },\n                                whileInView: {\n                                    opacity: 1,\n                                    scale: 1\n                                },\n                                viewport: {\n                                    once: true\n                                },\n                                transition: {\n                                    duration: 0.4,\n                                    delay: index * 0.05\n                                },\n                                className: \"group relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-12 h-12 md:w-16 md:h-16 bg-white/10 backdrop-blur-sm rounded-xl border border-white/20 flex items-center justify-center hover:bg-white/20 transition-all duration-300 hover:scale-110\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-8 h-8 md:w-10 md:h-10 bg-white rounded-lg flex items-center justify-center p-1\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                src: provider.logo,\n                                                alt: provider.alt,\n                                                width: 32,\n                                                height: 32,\n                                                className: \"w-full h-full object-contain\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\AIIntegrationsSection.tsx\",\n                                                lineNumber: 100,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\AIIntegrationsSection.tsx\",\n                                            lineNumber: 99,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\AIIntegrationsSection.tsx\",\n                                        lineNumber: 98,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute -top-10 left-1/2 transform -translate-x-1/2 bg-black/80 text-white text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 whitespace-nowrap\",\n                                        children: provider.alt\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\AIIntegrationsSection.tsx\",\n                                        lineNumber: 111,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, provider.name, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\AIIntegrationsSection.tsx\",\n                                lineNumber: 90,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\AIIntegrationsSection.tsx\",\n                        lineNumber: 82,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 30\n                        },\n                        whileInView: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        viewport: {\n                            once: true\n                        },\n                        transition: {\n                            duration: 0.8,\n                            delay: 0.4\n                        },\n                        className: \"grid grid-cols-6 md:grid-cols-12 gap-4 mb-12\",\n                        children: aiProviders.slice(12, 24).map((provider, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    scale: 0.8\n                                },\n                                whileInView: {\n                                    opacity: 1,\n                                    scale: 1\n                                },\n                                viewport: {\n                                    once: true\n                                },\n                                transition: {\n                                    duration: 0.4,\n                                    delay: index * 0.05\n                                },\n                                className: \"group relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-12 h-12 md:w-16 md:h-16 bg-white/10 backdrop-blur-sm rounded-xl border border-white/20 flex items-center justify-center hover:bg-white/20 transition-all duration-300 hover:scale-110\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-8 h-8 md:w-10 md:h-10 bg-white rounded-lg flex items-center justify-center p-1\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                src: provider.logo,\n                                                alt: provider.alt,\n                                                width: 32,\n                                                height: 32,\n                                                className: \"w-full h-full object-contain\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\AIIntegrationsSection.tsx\",\n                                                lineNumber: 137,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\AIIntegrationsSection.tsx\",\n                                            lineNumber: 136,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\AIIntegrationsSection.tsx\",\n                                        lineNumber: 135,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute -top-10 left-1/2 transform -translate-x-1/2 bg-black/80 text-white text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 whitespace-nowrap\",\n                                        children: provider.alt\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\AIIntegrationsSection.tsx\",\n                                        lineNumber: 148,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, provider.name, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\AIIntegrationsSection.tsx\",\n                                lineNumber: 127,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\AIIntegrationsSection.tsx\",\n                        lineNumber: 119,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 20\n                        },\n                        whileInView: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        viewport: {\n                            once: true\n                        },\n                        transition: {\n                            duration: 0.6,\n                            delay: 0.6\n                        },\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-400 mb-6\",\n                                children: \"And 270+ more providers available through RouKey's unified API\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\AIIntegrationsSection.tsx\",\n                                lineNumber: 163,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col sm:flex-row gap-4 justify-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"px-8 py-3 bg-gradient-to-r from-[#ff6b35] to-[#f7931e] text-white font-semibold rounded-xl hover:shadow-lg hover:shadow-[#ff6b35]/25 transition-all duration-300 hover:scale-105\",\n                                        children: \"View All Integrations\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\AIIntegrationsSection.tsx\",\n                                        lineNumber: 167,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"px-8 py-3 border border-white/20 text-white font-semibold rounded-xl hover:bg-white/10 transition-all duration-300\",\n                                        children: \"Start Free Trial\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\AIIntegrationsSection.tsx\",\n                                        lineNumber: 170,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\AIIntegrationsSection.tsx\",\n                                lineNumber: 166,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\AIIntegrationsSection.tsx\",\n                        lineNumber: 156,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\AIIntegrationsSection.tsx\",\n                lineNumber: 60,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\AIIntegrationsSection.tsx\",\n        lineNumber: 38,\n        columnNumber: 5\n    }, this);\n}\n_c = AIIntegrationsSection;\nvar _c;\n$RefreshReg$(_c, \"AIIntegrationsSection\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/landing/AIIntegrationsSection.tsx\n"));

/***/ })

});