'use client';

import { motion } from 'framer-motion';
import Image from 'next/image';

const aiProviders = [
  // Row 1 - Top providers with confirmed working logos
  { name: 'OpenAI', logo: 'https://raw.githubusercontent.com/lobehub/lobe-icons/master/packages/static-png/dark/openai.png', alt: 'OpenAI GPT Models' },
  { name: '<PERSON><PERSON><PERSON>', logo: 'https://raw.githubusercontent.com/lobehub/lobe-icons/master/packages/static-png/dark/alibaba-color.png', alt: 'Alibaba Qwen Models' },
  { name: 'Mistral AI', logo: 'https://raw.githubusercontent.com/lobehub/lobe-icons/master/packages/static-png/dark/mistral-color.png', alt: 'Mistral AI' },
  { name: 'Meta', logo: 'https://raw.githubusercontent.com/lobehub/lobe-icons/master/packages/static-png/dark/meta-color.png', alt: 'Meta Llama Models' },
  { name: 'Anthropic', logo: 'https://raw.githubusercontent.com/lobehub/lobe-icons/master/packages/static-png/dark/claude.png', alt: 'Anthropic Claude' },
  { name: 'DeepSeek', logo: 'https://raw.githubusercontent.com/lobehub/lobe-icons/master/packages/static-png/dark/deepseek.png', alt: 'DeepSeek' },
  { name: 'Google', logo: 'https://raw.githubusercontent.com/lobehub/lobe-icons/master/packages/static-png/dark/google-color.png', alt: 'Google Gemini' },
  { name: 'Microsoft', logo: 'https://raw.githubusercontent.com/lobehub/lobe-icons/master/packages/static-png/dark/azure-color.png', alt: 'Microsoft Azure' },
  { name: 'Cohere', logo: 'https://raw.githubusercontent.com/lobehub/lobe-icons/master/packages/static-png/dark/cohere-color.png', alt: 'Cohere' },
  { name: 'Perplexity', logo: 'https://raw.githubusercontent.com/lobehub/lobe-icons/master/packages/static-png/dark/perplexity-color.png', alt: 'Perplexity' },
  { name: 'xAI', logo: 'https://raw.githubusercontent.com/lobehub/lobe-icons/master/packages/static-png/dark/xai.png', alt: 'xAI Grok' },
  { name: 'NVIDIA', logo: 'https://raw.githubusercontent.com/lobehub/lobe-icons/master/packages/static-png/dark/nvidia-color.png', alt: 'NVIDIA' },

  // Row 2 - Additional providers with confirmed logos
  { name: 'AI21 Labs', logo: 'https://raw.githubusercontent.com/lobehub/lobe-icons/master/packages/static-png/dark/ai21.png', alt: 'AI21 Labs' },
  { name: 'Amazon', logo: 'https://raw.githubusercontent.com/lobehub/lobe-icons/master/packages/static-png/dark/aws-color.png', alt: 'Amazon Bedrock' },
  { name: 'Zhipu AI', logo: 'https://raw.githubusercontent.com/lobehub/lobe-icons/master/packages/static-png/dark/zhipu-color.png', alt: 'Zhipu AI GLM' },
  { name: 'Yi AI', logo: 'https://raw.githubusercontent.com/lobehub/lobe-icons/master/packages/static-png/dark/yi-color.png', alt: 'Yi AI' },
  { name: 'Hugging Face', logo: 'https://raw.githubusercontent.com/lobehub/lobe-icons/master/packages/static-png/dark/huggingface-color.png', alt: 'Hugging Face' },
  { name: 'Groq', logo: 'https://raw.githubusercontent.com/lobehub/lobe-icons/master/packages/static-png/dark/groq.png', alt: 'Groq' },
  { name: 'Together AI', logo: 'https://raw.githubusercontent.com/lobehub/lobe-icons/master/packages/static-png/dark/togetherai-color.png', alt: 'Together AI' },
  { name: 'Fireworks', logo: 'https://raw.githubusercontent.com/lobehub/lobe-icons/master/packages/static-png/dark/fireworksai-color.png', alt: 'Fireworks AI' },
  { name: 'Replicate', logo: 'https://raw.githubusercontent.com/lobehub/lobe-icons/master/packages/static-png/dark/replicate-color.png', alt: 'Replicate' },
  { name: 'Cerebras', logo: 'https://raw.githubusercontent.com/lobehub/lobe-icons/master/packages/static-png/dark/cerebras-color.png', alt: 'Cerebras' },
  { name: 'SambaNova', logo: 'https://raw.githubusercontent.com/lobehub/lobe-icons/master/packages/static-png/dark/sambanova-color.png', alt: 'SambaNova' },
  { name: 'Minimax', logo: 'https://raw.githubusercontent.com/lobehub/lobe-icons/master/packages/static-png/dark/minimax-color.png', alt: 'Minimax' }
];

export default function AIIntegrationsSection() {
  return (
    <section className="relative py-20 overflow-hidden">
      {/* Background with same gradient as main page */}
      <div 
        className="absolute inset-0"
        style={{
          background: `radial-gradient(ellipse at center, #1C051C 0%, #040716 70%)`
        }}
      />
      
      {/* Grid overlay */}
      <div className="absolute inset-0 opacity-20">
        <div
          style={{
            backgroundImage: `
              linear-gradient(rgba(255, 255, 255, 0.05) 1px, transparent 1px),
              linear-gradient(90deg, rgba(255, 255, 255, 0.05) 1px, transparent 1px)
            `,
            backgroundSize: '50px 50px'
          }}
        />
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
            Connect to any AI model with{' '}
            <span className="text-transparent bg-clip-text bg-gradient-to-r from-[#ff6b35] to-[#f7931e]">
              300+ integrations
            </span>
          </h2>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto">
            RouKey provides unified access to every major AI provider through a single API. 
            No vendor lock-in, just seamless AI integration.
          </p>
        </motion.div>

        {/* AI Provider Logos Grid */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.8, delay: 0.2 }}
          className="grid grid-cols-6 md:grid-cols-12 gap-4 mb-8"
        >
          {aiProviders.slice(0, 12).map((provider, index) => (
            <motion.div
              key={provider.name}
              initial={{ opacity: 0, scale: 0.8 }}
              whileInView={{ opacity: 1, scale: 1 }}
              viewport={{ once: true }}
              transition={{ duration: 0.4, delay: index * 0.05 }}
              className="group relative"
            >
              <div className="w-12 h-12 md:w-16 md:h-16 bg-white/10 backdrop-blur-sm rounded-xl border border-white/20 flex items-center justify-center hover:bg-white/20 transition-all duration-300 hover:scale-110">
                <div className="w-8 h-8 md:w-10 md:h-10 bg-white rounded-lg flex items-center justify-center p-1">
                  <Image
                    src={provider.logo}
                    alt={provider.alt}
                    width={32}
                    height={32}
                    className="w-full h-full object-contain"
                  />
                </div>
              </div>
              
              {/* Tooltip */}
              <div className="absolute -top-10 left-1/2 transform -translate-x-1/2 bg-black/80 text-white text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 whitespace-nowrap">
                {provider.alt}
              </div>
            </motion.div>
          ))}
        </motion.div>

        {/* Second Row */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.8, delay: 0.4 }}
          className="grid grid-cols-6 md:grid-cols-12 gap-4 mb-12"
        >
          {aiProviders.slice(12, 24).map((provider, index) => (
            <motion.div
              key={provider.name}
              initial={{ opacity: 0, scale: 0.8 }}
              whileInView={{ opacity: 1, scale: 1 }}
              viewport={{ once: true }}
              transition={{ duration: 0.4, delay: index * 0.05 }}
              className="group relative"
            >
              <div className="w-12 h-12 md:w-16 md:h-16 bg-white/10 backdrop-blur-sm rounded-xl border border-white/20 flex items-center justify-center hover:bg-white/20 transition-all duration-300 hover:scale-110">
                <div className="w-8 h-8 md:w-10 md:h-10 bg-white rounded-lg flex items-center justify-center p-1">
                  <Image
                    src={provider.logo}
                    alt={provider.alt}
                    width={32}
                    height={32}
                    className="w-full h-full object-contain"
                  />
                </div>
              </div>
              
              {/* Tooltip */}
              <div className="absolute -top-10 left-1/2 transform -translate-x-1/2 bg-black/80 text-white text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 whitespace-nowrap">
                {provider.alt}
              </div>
            </motion.div>
          ))}
        </motion.div>

        {/* Call to Action */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6, delay: 0.6 }}
          className="text-center"
        >
          <p className="text-gray-400 mb-6">
            And 270+ more providers available through RouKey's unified API
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <button className="px-8 py-3 bg-gradient-to-r from-[#ff6b35] to-[#f7931e] text-white font-semibold rounded-xl hover:shadow-lg hover:shadow-[#ff6b35]/25 transition-all duration-300 hover:scale-105">
              View All Integrations
            </button>
            <button className="px-8 py-3 border border-white/20 text-white font-semibold rounded-xl hover:bg-white/10 transition-all duration-300">
              Start Free Trial
            </button>
          </div>
        </motion.div>
      </div>
    </section>
  );
}
