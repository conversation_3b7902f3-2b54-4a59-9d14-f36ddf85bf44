# Download LLM provider logos from LobeHub CDN
# This script downloads SVG logos for all providers listed in AIIntegrationsSection.tsx

$logosDir = "public/logos"
$baseUrl = "https://registry.npmmirror.com/@lobehub/icons-static-svg/latest/files/dark"

# Create logos directory if it doesn't exist
if (!(Test-Path $logosDir)) {
    New-Item -ItemType Directory -Path $logosDir -Force
}

# Define all the logos we need to download
$logos = @{
    "openai" = "openai.svg"
    "anthropic" = "anthropic.svg"
    "google" = "google.svg"
    "meta" = "meta.svg"
    "mistral" = "mistral.svg"
    "cohere" = "cohere.svg"
    "perplexity" = "perplexity.svg"
    "fireworks" = "fireworks.svg"
    "together" = "together.svg"
    "replicate" = "replicate.svg"
    "deepseek" = "deepseek.svg"
    "aws" = "aws.svg"
    "azure" = "azure.svg"
    "gcp" = "google.svg"  # Using Google logo for GCP/Vertex
    "cerebras" = "cerebras.svg"
    "sambanova" = "sambanova.svg"
    "novita" = "novita.svg"
    "lepton" = "leptonai.svg"
    "hyperbolic" = "hyperbolic.svg"
    "xai" = "xai.svg"
    "ai21" = "ai21.svg"
    "anyscale" = "anyscale.svg"
}

Write-Host "Downloading LLM provider logos..." -ForegroundColor Green

foreach ($provider in $logos.Keys) {
    $filename = $logos[$provider]
    $url = "$baseUrl/$filename"
    $outputPath = "$logosDir/$provider.svg"
    
    try {
        Write-Host "Downloading $provider logo..." -ForegroundColor Yellow
        Invoke-WebRequest -Uri $url -OutFile $outputPath -ErrorAction Stop
        Write-Host "✓ Downloaded $provider.svg" -ForegroundColor Green
    }
    catch {
        Write-Host "✗ Failed to download $provider.svg: $($_.Exception.Message)" -ForegroundColor Red
        
        # Try alternative URLs for some providers
        $alternativeUrls = @{
            "gcp" = "https://registry.npmmirror.com/@lobehub/icons-static-svg/latest/files/dark/vertexai.svg"
            "lepton" = "https://registry.npmmirror.com/@lobehub/icons-static-svg/latest/files/dark/leptonai.svg"
        }

        if ($alternativeUrls.ContainsKey($provider)) {
            try {
                Write-Host "Trying alternative URL for $provider..." -ForegroundColor Yellow
                Invoke-WebRequest -Uri $alternativeUrls[$provider] -OutFile $outputPath -ErrorAction Stop
                Write-Host "✓ Downloaded $provider.svg (alternative)" -ForegroundColor Green
            }
            catch {
                Write-Host "✗ Alternative download also failed for $provider" -ForegroundColor Red
            }
        }
    }
}

Write-Host "`nLogo download complete!" -ForegroundColor Green
Write-Host "Downloaded logos to: $logosDir" -ForegroundColor Cyan

# List downloaded files
Write-Host "`nDownloaded files:" -ForegroundColor Cyan
Get-ChildItem $logosDir -Name | Sort-Object
